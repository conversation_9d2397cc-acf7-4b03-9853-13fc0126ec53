import { HandlerExecutionContext, <PERSON>ializeHandler, SerializeHandlerArguments } from "@smithy/types";
import { PreviouslyResolved } from "./schema-middleware-types";
/**
 * @internal
 */
export declare const schemaSerializationMiddleware: (config: PreviouslyResolved) => (next: SerializeHandler<any, any>, context: HandlerExecutionContext) => (args: SerializeHandlerArguments<any>) => Promise<import("@smithy/types").SerializeHandlerOutput<any>>;
