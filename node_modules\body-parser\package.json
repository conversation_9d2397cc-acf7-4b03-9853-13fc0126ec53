{"_args": [["body-parser", "F:\\zMouse\\NodeJS\\Blog2"]], "_from": "body-parser@*", "_id": "body-parser@1.15.2", "_inCache": true, "_installable": true, "_location": "/body-parser", "_nodeVersion": "4.4.3", "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/body-parser-1.15.2.tgz_1466393694089_0.7908455491997302"}, "_npmUser": {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, "_npmVersion": "2.15.1", "_phantomChildren": {}, "_requested": {"name": "body-parser", "raw": "body-parser", "rawSpec": "", "scope": null, "spec": "*", "type": "range"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/body-parser/-/body-parser-1.15.2.tgz", "_shasum": "d7578cf4f1d11d5f6ea804cef35dc7a7ff6dae67", "_shrinkwrap": null, "_spec": "body-parser", "_where": "F:\\zMouse\\NodeJS\\Blog2", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "dependencies": {"bytes": "2.4.0", "content-type": "~1.0.2", "debug": "~2.2.0", "depd": "~1.1.0", "http-errors": "~1.5.0", "iconv-lite": "0.4.13", "on-finished": "~2.3.0", "qs": "6.2.0", "raw-body": "~2.1.7", "type-is": "~1.6.13"}, "description": "Node.js body parsing middleware", "devDependencies": {"eslint": "2.13.0", "eslint-config-standard": "5.3.1", "eslint-plugin-promise": "1.3.2", "eslint-plugin-standard": "1.3.2", "istanbul": "0.4.3", "methods": "1.1.2", "mocha": "2.5.3", "supertest": "1.1.0"}, "directories": {}, "dist": {"shasum": "d7578cf4f1d11d5f6ea804cef35dc7a7ff6dae67", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.15.2.tgz"}, "engines": {"node": ">= 0.8"}, "files": ["HISTORY.md", "LICENSE", "index.js", "lib/"], "gitHead": "3c8218446d919a5e87fa696971fb7f69b10afc1c", "homepage": "https://github.com/expressjs/body-parser#readme", "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "name": "body-parser", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/expressjs/body-parser.git"}, "scripts": {"lint": "eslint **/*.js", "test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "version": "1.15.2"}