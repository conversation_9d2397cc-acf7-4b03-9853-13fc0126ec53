{"_from": "concat-stream@^1.5.2", "_id": "concat-stream@1.6.2", "_inBundle": false, "_integrity": "sha512-27HBghJxjiZtIk3Ycvn/4kbJk/1uZuJFfuPEns6LaEvpvG1f0hTea8lilrouyo9mVc2GWdcEZ8OLoGmSADlrCw==", "_location": "/concat-stream", "_phantomChildren": {"core-util-is": "1.0.2", "process-nextick-args": "2.0.1", "safe-buffer": "5.1.2", "util-deprecate": "1.0.2"}, "_requested": {"type": "range", "registry": true, "raw": "concat-stream@^1.5.2", "name": "concat-stream", "escapedName": "concat-stream", "rawSpec": "^1.5.2", "saveSpec": null, "fetchSpec": "^1.5.2"}, "_requiredBy": ["/multer"], "_resolved": "https://registry.npmjs.org/concat-stream/-/concat-stream-1.6.2.tgz", "_shasum": "904bdf194cd3122fc675c77fc4ac3d4ff0fd1a34", "_spec": "concat-stream@^1.5.2", "_where": "F:\\node.js-sample\\node_modules\\multer", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "http://github.com/maxogden/concat-stream/issues"}, "bundleDependencies": false, "dependencies": {"buffer-from": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}, "deprecated": false, "description": "writable stream that concatenates strings or binary data and calls a callback with the result", "devDependencies": {"tape": "^4.6.3"}, "engines": ["node >= 0.8"], "files": ["index.js"], "homepage": "https://github.com/maxogden/concat-stream#readme", "license": "MIT", "main": "index.js", "name": "concat-stream", "repository": {"type": "git", "url": "git+ssh://**************/maxogden/concat-stream.git"}, "scripts": {"test": "tape test/*.js test/server/*.js"}, "tags": ["stream", "simple", "util", "utility"], "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/17..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "version": "1.6.2"}