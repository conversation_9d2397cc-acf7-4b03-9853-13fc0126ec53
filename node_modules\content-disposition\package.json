{"_args": [["content-disposition@0.5.1", "F:\\zMouse\\NodeJS\\Blog2\\node_modules\\express"]], "_from": "content-disposition@0.5.1", "_id": "content-disposition@0.5.1", "_inCache": true, "_installable": true, "_location": "/content-disposition", "_npmUser": {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, "_npmVersion": "1.4.28", "_phantomChildren": {}, "_requested": {"name": "content-disposition", "raw": "content-disposition@0.5.1", "rawSpec": "0.5.1", "scope": null, "spec": "0.5.1", "type": "version"}, "_requiredBy": ["/express"], "_resolved": "https://registry.npmjs.org/content-disposition/-/content-disposition-0.5.1.tgz", "_shasum": "87476c6a67c8daa87e32e87616df883ba7fb071b", "_shrinkwrap": null, "_spec": "content-disposition@0.5.1", "_where": "F:\\zMouse\\NodeJS\\Blog2\\node_modules\\express", "bugs": {"url": "https://github.com/jshttp/content-disposition/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {}, "description": "Create and parse Content-Disposition header", "devDependencies": {"istanbul": "0.4.2", "mocha": "1.21.5"}, "directories": {}, "dist": {"shasum": "87476c6a67c8daa87e32e87616df883ba7fb071b", "tarball": "https://registry.npmjs.org/content-disposition/-/content-disposition-0.5.1.tgz"}, "engines": {"node": ">= 0.6"}, "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "gitHead": "7b391db3af5629d4c698f1de21802940bb9f22a5", "homepage": "https://github.com/jshttp/content-disposition", "keywords": ["content-disposition", "http", "res", "rfc6266"], "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "name": "content-disposition", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/jshttp/content-disposition.git"}, "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "version": "0.5.1"}