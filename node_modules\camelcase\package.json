{"_args": [["camelcase@^1.0.2", "F:\\zMouse\\NodeJS\\Blog2\\node_modules\\yargs"]], "_from": "camelcase@>=1.0.2 <2.0.0", "_id": "camelcase@1.2.1", "_inCache": true, "_installable": true, "_location": "/camelcase", "_nodeVersion": "0.12.5", "_npmUser": {"email": "<EMAIL>", "name": "sindresor<PERSON>"}, "_npmVersion": "2.11.2", "_phantomChildren": {}, "_requested": {"name": "camelcase", "raw": "camelcase@^1.0.2", "rawSpec": "^1.0.2", "scope": null, "spec": ">=1.0.2 <2.0.0", "type": "range"}, "_requiredBy": ["/yargs"], "_resolved": "https://registry.npmjs.org/camelcase/-/camelcase-1.2.1.tgz", "_shasum": "9bb5304d2e0b56698b2c758b08a3eaa9daa58a39", "_shrinkwrap": null, "_spec": "camelcase@^1.0.2", "_where": "F:\\zMouse\\NodeJS\\Blog2\\node_modules\\yargs", "author": {"email": "<EMAIL>", "name": "<PERSON><PERSON>", "url": "http://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/camelcase/issues"}, "dependencies": {}, "description": "Convert a dash/dot/underscore/space separated string to camelCase: foo-bar → fooBar", "devDependencies": {"ava": "0.0.4"}, "directories": {}, "dist": {"shasum": "9bb5304d2e0b56698b2c758b08a3eaa9daa58a39", "tarball": "https://registry.npmjs.org/camelcase/-/camelcase-1.2.1.tgz"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "gitHead": "185ba12da723be9c1ee986cc2956bdc4c517a141", "homepage": "https://github.com/sindresorhus/camelcase", "keywords": ["camel", "camel-case", "camelcase", "case", "convert", "dash", "dot", "hyphen", "separator", "string", "text", "underscore"], "license": "MIT", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "name": "camelcase", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/camelcase.git"}, "scripts": {"test": "node test.js"}, "version": "1.2.1"}