{"_from": "append-field@^1.0.0", "_id": "append-field@1.0.0", "_inBundle": false, "_integrity": "sha512-klpgFSWLW1ZEs8svjfb7g4qWY0YS5imI82dTg+QahUvJ8YqAY0P10Uk8tTyh9ZGuYEZEMaeJYCF5BFuX552hsw==", "_location": "/append-field", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "append-field@^1.0.0", "name": "append-field", "escapedName": "append-field", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/multer"], "_resolved": "https://registry.npmjs.org/append-field/-/append-field-1.0.0.tgz", "_shasum": "1e3440e915f0b1203d23748e78edd7b9b5b43e56", "_spec": "append-field@^1.0.0", "_where": "F:\\node.js-sample\\node_modules\\multer", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/LinusU/node-append-field/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A [W3C HTML JSON forms spec](http://www.w3.org/TR/html-json-forms/) compliant field appender (for lack of a better name). Useful for people implementing `application/x-www-form-urlencoded` and `multipart/form-data` parsers.", "devDependencies": {"mocha": "^2.2.4", "standard": "^6.0.5", "testdata-w3c-json-form": "^0.2.0"}, "homepage": "https://github.com/LinusU/node-append-field#readme", "license": "MIT", "main": "index.js", "name": "append-field", "repository": {"type": "git", "url": "git+ssh://**************/LinusU/node-append-field.git"}, "scripts": {"test": "standard && mocha"}, "version": "1.0.0"}