{"_args": [["accepts@~1.3.3", "F:\\zMouse\\NodeJS\\Blog2\\node_modules\\express"]], "_from": "accepts@>=1.3.3 <1.4.0", "_id": "accepts@1.3.3", "_inCache": true, "_installable": true, "_location": "/accepts", "_nodeVersion": "4.4.3", "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/accepts-1.3.3.tgz_1462251932032_0.7092335098423064"}, "_npmUser": {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, "_npmVersion": "2.15.1", "_phantomChildren": {}, "_requested": {"name": "accepts", "raw": "accepts@~1.3.3", "rawSpec": "~1.3.3", "scope": null, "spec": ">=1.3.3 <1.4.0", "type": "range"}, "_requiredBy": ["/express"], "_resolved": "https://registry.npmjs.org/accepts/-/accepts-1.3.3.tgz", "_shasum": "c3ca7434938648c3e0d9c1e328dd68b622c284ca", "_shrinkwrap": null, "_spec": "accepts@~1.3.3", "_where": "F:\\zMouse\\NodeJS\\Blog2\\node_modules\\express", "bugs": {"url": "https://github.com/jshttp/accepts/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "dependencies": {"mime-types": "~2.1.11", "negotiator": "0.6.1"}, "description": "Higher-level content negotiation", "devDependencies": {"istanbul": "0.4.3", "mocha": "~1.21.5"}, "directories": {}, "dist": {"shasum": "c3ca7434938648c3e0d9c1e328dd68b622c284ca", "tarball": "https://registry.npmjs.org/accepts/-/accepts-1.3.3.tgz"}, "engines": {"node": ">= 0.6"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "gitHead": "3e925b1e65ed7da2798849683d49814680dfa426", "homepage": "https://github.com/jshttp/accepts#readme", "keywords": ["accept", "accepts", "content", "negotiation"], "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "name": "accepts", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/jshttp/accepts.git"}, "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "version": "1.3.3"}