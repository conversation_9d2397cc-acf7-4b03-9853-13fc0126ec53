{"_args": [["array-flatten@1.1.1", "F:\\zMouse\\NodeJS\\Blog2\\node_modules\\express"]], "_from": "array-flatten@1.1.1", "_id": "array-flatten@1.1.1", "_inCache": true, "_installable": true, "_location": "/array-flatten", "_nodeVersion": "2.3.3", "_npmUser": {"email": "<EMAIL>", "name": "b<PERSON><PERSON><PERSON><PERSON>"}, "_npmVersion": "2.11.3", "_phantomChildren": {}, "_requested": {"name": "array-flatten", "raw": "array-flatten@1.1.1", "rawSpec": "1.1.1", "scope": null, "spec": "1.1.1", "type": "version"}, "_requiredBy": ["/express"], "_resolved": "https://registry.npmjs.org/array-flatten/-/array-flatten-1.1.1.tgz", "_shasum": "9a5f699051b1e7073328f2a008968b64ea2955d2", "_shrinkwrap": null, "_spec": "array-flatten@1.1.1", "_where": "F:\\zMouse\\NodeJS\\Blog2\\node_modules\\express", "author": {"email": "<EMAIL>", "name": "<PERSON>", "url": "http://blakeembrey.me"}, "bugs": {"url": "https://github.com/blakeembrey/array-flatten/issues"}, "dependencies": {}, "description": "Flatten an array of nested arrays into a single flat array", "devDependencies": {"istanbul": "^0.3.13", "mocha": "^2.2.4", "pre-commit": "^1.0.7", "standard": "^3.7.3"}, "directories": {}, "dist": {"shasum": "9a5f699051b1e7073328f2a008968b64ea2955d2", "tarball": "https://registry.npmjs.org/array-flatten/-/array-flatten-1.1.1.tgz"}, "files": ["LICENSE", "array-flatten.js"], "gitHead": "1963a9189229d408e1e8f585a00c8be9edbd1803", "homepage": "https://github.com/blakeembrey/array-flatten", "keywords": ["arguments", "array", "depth", "flatten"], "license": "MIT", "main": "array-flatten.js", "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "name": "array-flatten", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git://github.com/blakeembrey/array-flatten.git"}, "scripts": {"test": "istanbul cover _mocha -- -R spec"}, "version": "1.1.1"}