{"_args": [["amdefine@>=0.0.4", "F:\\zMouse\\NodeJS\\Blog2\\node_modules\\source-map"]], "_from": "amdefine@>=0.0.4", "_id": "amdefine@1.0.0", "_inCache": true, "_installable": true, "_location": "/amdefine", "_nodeVersion": "0.10.36", "_npmUser": {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON>"}, "_npmVersion": "2.12.1", "_phantomChildren": {}, "_requested": {"name": "amdefine", "raw": "amdefine@>=0.0.4", "rawSpec": ">=0.0.4", "scope": null, "spec": ">=0.0.4", "type": "range"}, "_requiredBy": ["/source-map"], "_resolved": "https://registry.npmjs.org/amdefine/-/amdefine-1.0.0.tgz", "_shasum": "fd17474700cb5cc9c2b709f0be9d23ce3c198c33", "_shrinkwrap": null, "_spec": "amdefine@>=0.0.4", "_where": "F:\\zMouse\\NodeJS\\Blog2\\node_modules\\source-map", "author": {"email": "<EMAIL>", "name": "<PERSON>", "url": "http://github.com/jrburke"}, "bugs": {"url": "https://github.com/jrburke/amdefine/issues"}, "dependencies": {}, "description": "Provide AMD's define() API for declaring modules in the AMD format", "devDependencies": {}, "directories": {}, "dist": {"shasum": "fd17474700cb5cc9c2b709f0be9d23ce3c198c33", "tarball": "https://registry.npmjs.org/amdefine/-/amdefine-1.0.0.tgz"}, "engines": {"node": ">=0.4.2"}, "gitHead": "578bc4a3f7dede33f3f3e10edde0c1607005d761", "homepage": "http://github.com/jrburke/amdefine", "license": "BSD-3-Clause AND MIT", "main": "./amdefine.js", "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "name": "amdefine", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/jrburke/amdefine.git"}, "scripts": {}, "version": "1.0.0"}