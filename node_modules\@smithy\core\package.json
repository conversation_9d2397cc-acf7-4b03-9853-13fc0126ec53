{"name": "@smithy/core", "version": "3.5.3", "scripts": {"build": "yarn lint && concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types && yarn build:types:downlevel'", "build:cjs": "node ../../scripts/inline core", "build:es": "yarn g:tsc -p tsconfig.es.json", "build:types": "yarn g:tsc -p tsconfig.types.json", "build:types:downlevel": "rimraf dist-types/ts3.4 && downlevel-dts dist-types dist-types/ts3.4", "stage-release": "rimraf ./.release && yarn pack && mkdir ./.release && tar zxvf ./package.tgz --directory ./.release && rm ./package.tgz", "clean": "rimraf ./dist-* && rimraf *.tsbuildinfo || exit 0", "lint": "npx eslint -c ../../.eslintrc.js \"src/**/*.ts\" --fix && node ./scripts/lint", "format": "prettier --config ../../prettier.config.js --ignore-path ../../.prettierignore --write \"**/*.{ts,md,json}\"", "extract:docs": "api-extractor run --local", "test:cbor:perf": "node ./scripts/cbor-perf.mjs", "test": "yarn g:vitest run", "test:watch": "yarn g:vitest watch"}, "main": "./dist-cjs/index.js", "module": "./dist-es/index.js", "types": "./dist-types/index.d.ts", "exports": {".": {"module": "./dist-es/index.js", "node": "./dist-cjs/index.js", "import": "./dist-es/index.js", "require": "./dist-cjs/index.js", "types": "./dist-types/index.d.ts"}, "./package.json": {"module": "./package.json", "node": "./package.json", "import": "./package.json", "require": "./package.json"}, "./cbor": {"module": "./dist-es/submodules/cbor/index.js", "node": "./dist-cjs/submodules/cbor/index.js", "import": "./dist-es/submodules/cbor/index.js", "require": "./dist-cjs/submodules/cbor/index.js", "types": "./dist-types/submodules/cbor/index.d.ts"}, "./protocols": {"module": "./dist-es/submodules/protocols/index.js", "node": "./dist-cjs/submodules/protocols/index.js", "import": "./dist-es/submodules/protocols/index.js", "require": "./dist-cjs/submodules/protocols/index.js", "types": "./dist-types/submodules/protocols/index.d.ts"}, "./serde": {"module": "./dist-es/submodules/serde/index.js", "node": "./dist-cjs/submodules/serde/index.js", "import": "./dist-es/submodules/serde/index.js", "require": "./dist-cjs/submodules/serde/index.js", "types": "./dist-types/submodules/serde/index.d.ts"}, "./schema": {"module": "./dist-es/submodules/schema/index.js", "node": "./dist-cjs/submodules/schema/index.js", "import": "./dist-es/submodules/schema/index.js", "require": "./dist-cjs/submodules/schema/index.js", "types": "./dist-types/submodules/schema/index.d.ts"}}, "author": {"name": "AWS Smithy Team", "email": "", "url": "https://smithy.io"}, "license": "Apache-2.0", "dependencies": {"@smithy/middleware-serde": "^4.0.8", "@smithy/protocol-http": "^5.1.2", "@smithy/types": "^4.3.1", "@smithy/util-base64": "^4.0.0", "@smithy/util-body-length-browser": "^4.0.0", "@smithy/util-middleware": "^4.0.4", "@smithy/util-stream": "^4.2.2", "@smithy/util-utf8": "^4.0.0", "tslib": "^2.6.2"}, "engines": {"node": ">=18.0.0"}, "typesVersions": {"<4.0": {"dist-types/*": ["dist-types/ts3.4/*"]}}, "files": ["./cbor.d.ts", "./cbor.js", "./protocols.d.ts", "./protocols.js", "./schema.d.ts", "./schema.js", "./serde.d.ts", "./serde.js", "dist-*/**"], "homepage": "https://github.com/smithy-lang/smithy-typescript/tree/main/packages/core", "repository": {"type": "git", "url": "https://github.com/smithy-lang/smithy-typescript.git", "directory": "packages/core"}, "devDependencies": {"@types/node": "^18.11.9", "concurrently": "7.0.0", "downlevel-dts": "0.10.1", "json-bigint": "^1.0.0", "rimraf": "3.0.2", "typedoc": "0.23.23"}, "typedoc": {"entryPoint": "src/index.ts"}, "publishConfig": {"directory": ".release/package"}}