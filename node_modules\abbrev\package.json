{"_args": [["abbrev@1", "F:\\zMouse\\NodeJS\\Blog2\\node_modules\\nopt"]], "_from": "abbrev@>=1.0.0 <2.0.0", "_id": "abbrev@1.0.9", "_inCache": true, "_installable": true, "_location": "/abbrev", "_nodeVersion": "4.4.4", "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/abbrev-1.0.9.tgz_1466016055839_0.7825860097073019"}, "_npmUser": {"email": "<EMAIL>", "name": "isaacs"}, "_npmVersion": "3.9.1", "_phantomChildren": {}, "_requested": {"name": "abbrev", "raw": "abbrev@1", "rawSpec": "1", "scope": null, "spec": ">=1.0.0 <2.0.0", "type": "range"}, "_requiredBy": ["/nopt"], "_resolved": "https://registry.npmjs.org/abbrev/-/abbrev-1.0.9.tgz", "_shasum": "91b4792588a7738c25f35dd6f63752a2f8776135", "_shrinkwrap": null, "_spec": "abbrev@1", "_where": "F:\\zMouse\\NodeJS\\Blog2\\node_modules\\nopt", "author": {"email": "<EMAIL>", "name": "<PERSON>"}, "bugs": {"url": "https://github.com/isaacs/abbrev-js/issues"}, "dependencies": {}, "description": "Like ruby's abbrev module, but in js", "devDependencies": {"tap": "^5.7.2"}, "directories": {}, "dist": {"shasum": "91b4792588a7738c25f35dd6f63752a2f8776135", "tarball": "https://registry.npmjs.org/abbrev/-/abbrev-1.0.9.tgz"}, "files": ["abbrev.js"], "gitHead": "c386cd9dbb1d8d7581718c54d4ba944cc9298d6f", "homepage": "https://github.com/isaacs/abbrev-js#readme", "license": "ISC", "main": "abbrev.js", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "name": "abbrev", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+ssh://**************/isaacs/abbrev-js.git"}, "scripts": {"test": "tap test.js --cov"}, "version": "1.0.9"}