{"_args": [["async@~0.2.6", "F:\\zMouse\\NodeJS\\Blog2\\node_modules\\uglify-js"]], "_from": "async@>=0.2.6 <0.3.0", "_id": "async@0.2.10", "_inCache": true, "_installable": true, "_location": "/async", "_npmUser": {"email": "<EMAIL>", "name": "caolan"}, "_npmVersion": "1.3.2", "_phantomChildren": {}, "_requested": {"name": "async", "raw": "async@~0.2.6", "rawSpec": "~0.2.6", "scope": null, "spec": ">=0.2.6 <0.3.0", "type": "range"}, "_requiredBy": ["/uglify-js"], "_resolved": "https://registry.npmjs.org/async/-/async-0.2.10.tgz", "_shasum": "b6bbe0b0674b9d719708ca38de8c237cb526c3d1", "_shrinkwrap": null, "_spec": "async@~0.2.6", "_where": "F:\\zMouse\\NodeJS\\Blog2\\node_modules\\uglify-js", "author": {"name": "<PERSON><PERSON>"}, "bugs": {"url": "https://github.com/caolan/async/issues"}, "dependencies": {}, "description": "Higher-order functions and common patterns for asynchronous code", "devDependencies": {"nodelint": ">0.0.0", "nodeunit": ">0.0.0", "uglify-js": "1.2.x"}, "directories": {}, "dist": {"shasum": "b6bbe0b0674b9d719708ca38de8c237cb526c3d1", "tarball": "https://registry.npmjs.org/async/-/async-0.2.10.tgz"}, "homepage": "https://github.com/caolan/async#readme", "jam": {"include": ["LICENSE", "README.md", "lib/async.js"], "main": "lib/async.js"}, "licenses": [{"type": "MIT", "url": "https://github.com/caolan/async/raw/master/LICENSE"}], "main": "./lib/async", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}], "name": "async", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/caolan/async.git"}, "scripts": {"test": "nodeunit test/test-async.js"}, "version": "0.2.10"}